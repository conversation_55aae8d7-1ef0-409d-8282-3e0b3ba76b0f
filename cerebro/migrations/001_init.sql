-- migrations/001_init.sql
CREATE TABLE users (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  display_name VA<PERSON><PERSON><PERSON>(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE roles (
  id TINYINT UNSIGNED PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL
);

CREATE TABLE features (
  id TINYINT UNSIGNED PRIMARY KEY,
  code VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
  description VARCHAR(255)
);

CREATE TABLE user_roles (
  user_id BIGINT UNSIGNED NOT NULL,
  role_id TINYINT UNSIGNED NOT NULL,
  PRIMARY KEY (user_id, role_id),
  FOR<PERSON><PERSON><PERSON> (user_id) REFERENCES users(id) ON DELETE CASCADE,
  <PERSON><PERSON>EI<PERSON><PERSON>Y (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

CREATE TABLE role_features (
  role_id TINYINT UNSIGNED NOT NULL,
  feature_id TINYINT UNSIGNED NOT NULL,
  PRIMARY KEY (role_id, feature_id),
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (feature_id) REFERENCES features(id) ON DELETE CASCADE
);

-- OIDC
CREATE TABLE oidc_clients (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  client_id VARCHAR(100) UNIQUE NOT NULL,
  client_secret VARCHAR(255),        -- opcional si PKCE-only
  redirect_uris TEXT NOT NULL,       -- JSON array
  name VARCHAR(100),
  allowed_scopes VARCHAR(255) DEFAULT 'openid profile email',
  require_pkce BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE auth_codes (
  code VARCHAR(128) PRIMARY KEY,
  client_id VARCHAR(100) NOT NULL,
  user_id BIGINT UNSIGNED NOT NULL,
  redirect_uri VARCHAR(500) NOT NULL,
  code_challenge VARCHAR(128) NOT NULL,
  code_challenge_method ENUM('S256') NOT NULL,
  scope VARCHAR(255) DEFAULT 'openid',
  expires_at TIMESTAMP NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE refresh_tokens (
  token VARCHAR(255) PRIMARY KEY,
  user_id BIGINT UNSIGNED NOT NULL,
  client_id VARCHAR(100) NOT NULL,
  revoked BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMP NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE jwk_keys (
  kid VARCHAR(64) PRIMARY KEY,
  kty VARCHAR(10) NOT NULL,
  alg VARCHAR(10) NOT NULL,
  use VARCHAR(10) NOT NULL,
  public_jwk JSON NOT NULL,
  private_jwk JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT FALSE
);
