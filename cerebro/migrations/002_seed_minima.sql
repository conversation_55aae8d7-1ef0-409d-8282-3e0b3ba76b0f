-- migrations/002_seed_minima.sql
INSERT IGNORE INTO roles (id, name) VALUES (1, 'ADMIN'), (2, 'CLIENT'), (3, 'PRO');
INSERT IGNORE INTO features (id, code, description)
VALUES (1, 'DASHBOARD', 'Acceso al dashboard'), (2, 'PREMIUM', 'Funciones premium');

-- admin inicial (password: admin123 cambia en cuanto entres)
INSERT INTO users (email, password_hash, display_name)
VALUES ('<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=1$REEMPLAZA$REEMPLAZA', 'Admin')
ON DUPLICATE KEY UPDATE email=email;

-- rol ADMIN
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT id, 1 FROM users WHERE email='<EMAIL>' LIMIT 1;

-- cliente OIDC de pruebas
INSERT IGNORE INTO oidc_clients (client_id, redirect_uris, name, require_pkce)
VALUES ('app-a-local', '["http://localhost:5173/auth/callback"]', 'App A local', TRUE);
