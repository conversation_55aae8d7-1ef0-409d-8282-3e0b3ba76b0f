// src/index.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import pinoHttp from 'pino-http';
import rateLimit from 'express-rate-limit';
import { env } from './env.js';
import { logger } from './logger.js';
import wellKnown from './routes/wellKnown.js';
import jwks from './routes/jwks.js';
import authorize from './routes/authorize.js';
import token from './routes/token.js';
import userinfo from './routes/userinfo.js';
import admin from './routes/admin.js';
import profile from './routes/profile.js';

const app = express();
app.use(helmet());
app.use(express.json());
app.use(cors({ origin: false })); // CORS cerrado; BFFs serán los orígenes permitidos más adelante
app.use(pinoHttp({ logger }));
app.use(rateLimit({ windowMs: 60_000, max: 120 }));

app.use('/.well-known', wellKnown);
app.use('/jwks.json', jwks);
app.use('/authorize', authorize);
app.use('/token', token);
app.use('/userinfo', userinfo);
app.use('/admin', admin);
app.use('/user', profile);

app.get('/health', (_, res) => res.json({ ok: true }));

app.listen(env.PORT, () => {
  logger.info(`cerebro-idp escuchando en :${env.PORT}`);
});
