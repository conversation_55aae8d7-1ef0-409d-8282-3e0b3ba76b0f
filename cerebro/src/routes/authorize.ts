// src/routes/authorize.ts
import { Router } from 'express';
import { z } from 'zod';
import { pool } from '../db.js';
const r = Router();

const Q = z.object({
  response_type: z.literal('code'),
  client_id: z.string(),
  redirect_uri: z.string().url(),
  scope: z.string().default('openid'),
  state: z.string().optional(),
  code_challenge: z.string(),
  code_challenge_method: z.literal('S256')
});

r.get('/', async (req, res) => {
  const parsed = Q.safeParse(req.query);
  if (!parsed.success) return res.status(400).json(parsed.error);
  const q = parsed.data;

  // validar client + redirect
  const [clients]: any[] = await pool.query('SELECT * FROM oidc_clients WHERE client_id=? LIMIT 1', [q.client_id]);
  if (!clients.length) return res.status(400).send('invalid_client');
  const client = clients[0];
  const allowed = JSON.parse(client.redirect_uris) as string[];
  if (!allowed.includes(q.redirect_uri)) return res.status(400).send('invalid_redirect_uri');

  // (DEMO) autenticación del usuario: en MVP permitimos ?login_hint=email&pwd=...
  const email = String(req.query.login_hint || '');
  const pwd = String(req.query.pwd || '');
  const [[user]]: any[] = await pool.query('SELECT * FROM users WHERE email=? LIMIT 1', [email]);
  if (!user) return res.status(401).send('login_required');

  // (en real → pantalla login; aquí simplificamos)
  const code = crypto.randomUUID().replace(/-/g, '');
  const exp = new Date(Date.now() + 5 * 60 * 1000); // 5 min
  await pool.query(
    'INSERT INTO auth_codes (code, client_id, user_id, redirect_uri, code_challenge, code_challenge_method, scope, expires_at) VALUES (?,?,?,?,?,?,?,?)',
    [code, q.client_id, user.id, q.redirect_uri, q.code_challenge, q.code_challenge_method, q.scope, exp]
  );

  const url = new URL(q.redirect_uri);
  url.searchParams.set('code', code);
  if (q.state) url.searchParams.set('state', q.state);
  return res.redirect(url.toString());
});

export default r;
