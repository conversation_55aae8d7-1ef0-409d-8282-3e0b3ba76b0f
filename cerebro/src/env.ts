// src/env.ts
import 'dotenv/config';
import { z } from 'zod';

const Env = z.object({
  PORT: z.coerce.number().default(4000),
  PUBLIC_BASE_URL: z.string().url(),
  DB_HOST: z.string(),
  DB_PORT: z.coerce.number(),
  DB_USER: z.string(),
  DB_PASSWORD: z.string(),
  DB_NAME: z.string(),
  REDIS_URL: z.string(),
  OIDC_ISSUER: z.string().url(),
  OIDC_AUDIENCE: z.string(),
  JWK_ACTIVE_KID: z.string().optional(),
});
export const env = Env.parse(process.env);
