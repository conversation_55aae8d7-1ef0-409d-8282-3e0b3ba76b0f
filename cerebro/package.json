{"name": "cerebro-idp", "type": "module", "scripts": {"dev": "nodemon --watch src --exec tsx src/index.ts", "build": "tsc -p tsconfig.json", "start": "node dist/index.js", "migrate": "tsx scripts/migrate.ts", "seed": "tsx scripts/seed.ts", "gen:jwk": "tsx scripts/gen-jwk.ts", "tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase|backup_*' > _docs/file-system.md"}, "dependencies": {"argon2": "^0.44.0", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "ioredis": "^5.7.0", "jose": "^6.1.0", "mysql2": "^3.14.4", "pino": "^9.9.1", "pino-http": "^10.5.0", "zod": "^4.1.5"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.3.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2"}}