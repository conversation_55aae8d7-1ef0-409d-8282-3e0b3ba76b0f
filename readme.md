"tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase|backup_*' > _docs/file-system.md"

Con Firebase Auth es mucho más directo. Te explico cómo sería:

## Configuración Base
- Proyecto Firebase único:
  - Un solo proyecto de Firebase para todas las 5 apps React
  - Mismo firebaseConfig en todas las aplicaciones
  - Habilitado Authentication con los providers que necesites (email/password, Google, etc.)
- Dominio autorizado:
  - En Firebase Console → Authentication → Settings → Authorized domains
  - Agregar todos los dominios donde estarán tus apps

## Flujo de SSO con Firebase

### Escenario:
1. Usuario se loguea en React 1 usando signInWithEmailAndPassword()
2. Firebase guarda el estado de auth automáticamente
3. Usuario navega a React 2 en otra pestaña/dominio
4. React 2 detecta automáticamente que ya está logueado

## Lo que necesitas en cada React:

### Instalación:
npm install firebase

### Estructura en cada app:
- firebase/config.js - Configuración compartida
- context/AuthContext.js - Estado global de auth
- hooks/useAuth.js - Lógica reutilizable
- components/ProtectedRoute.js - Protección de rutas

## Ventajas de Firebase Auth:

### Automático:
- Persistencia de sesión entre dominios/tabs
- Refresh automático de tokens
- Estado sincronizado en tiempo real
- Logout automático en todas las apps

### Sin backend propio:
- No necesitas crear servicio de auth
- No gestionar base de datos de usuarios
- No manejar tokens manualmente
- Escalabilidad automática

## Configuración por app:
Cada React app tendrá el mismo firebaseConfig pero puede tener:
- Diferentes rutas protegidas
- Diferentes roles/permisos (usando Custom Claims)
- UI personalizada de login

## Limitaciones a considerar:
- Todas las apps deben usar el mismo proyecto Firebase
- Dependes de un servicio externo
- Custom Claims requieren Cloud Functions para casos complejos
- Límites de cuota gratuita

# Web de control

## Identificación de Apps/Sitios
Sí, cada app React debería tener:
- App ID único (ej: app-dashboard, app-billing, app-reports, etc.)
- Configuración de acceso específica

## Sistema de Permisos Multicapa
- Roles generales del usuario:
  - admin
  - premium_user
  - basic_user
  - trial_user
- Permisos por aplicación:
{
  "user_id": "123",
  "roles": ["premium_user"],
  "app_permissions": {
    "app-dashboard": { "access": true, "level": "full" },
    "app-billing": { "access": true, "level": "read" },
    "app-reports": { "access": false, "reason": "requires_premium" },
    "app-analytics": { "access": true, "expires": "2024-12-31" }
  }
}

## Control de Acceso por Suscripción
En Firebase, usando Custom Claims:
{
  "subscription": "premium",
  "subscription_expires": "2024-12-31",
  "allowed_apps": ["app-1", "app-2", "app-3"],
  "features": ["export", "analytics", "api_access"]
}

## Flujo de Validación
Cuando usuario accede a React 2:
- Firebase Auth confirma identidad ✓
- App verifica: ¿puede acceder a "app-dashboard"?
- Chequea suscripción/pago
- Si no puede → redirect a página de upgrade/pago
- Si puede → acceso normal

## Implementación práctica
En cada React app:
// Al inicializar la app
const checkAppAccess = (user, appId) => {
  const claims = user.customClaims;
  return claims.allowed_apps?.includes(appId);
}

// Si no tiene acceso
if (!hasAccess) {
  // Redirect a landing de "Necesitas suscripción Premium"
}

## Backend (Cloud Functions):
- Función que actualiza Custom Claims según pagos
- Webhook de Stripe/PayPal que modifica permisos
- Cron job que revisa suscripciones expiradas
