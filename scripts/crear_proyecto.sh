#!/bin/bash

# Script para generar la estructura de directorios y ficheros
# del proyecto cerebro-idp.

echo "🚀 Iniciando la creación de la estructura del proyecto..."

# Directorio raíz del proyecto
ROOT_DIR="cerebro"

# Crear el directorio raíz y entrar en él
mkdir -p "$ROOT_DIR"
cd "$ROOT_DIR" || exit

# Crear todos los subdirectorios de una vez
echo "-> Creando directorios..."
mkdir -p \
  src/{crypto,middlewares,routes,services} \
  migrations \
  scripts

# Crear todos los ficheros vacíos de una vez
echo "-> Creando ficheros..."
touch \
  src/index.ts \
  src/env.ts \
  src/db.ts \
  src/logger.ts \
  src/crypto/jwk.ts \
  src/middlewares/rateLimit.ts \
  src/middlewares/requireAdmin.ts \
  src/routes/wellKnown.ts \
  src/routes/jwks.ts \
  src/routes/authorize.ts \
  src/routes/token.ts \
  src/routes/userinfo.ts \
  src/routes/admin.ts \
  src/routes/profile.ts \
  src/services/users.ts \
  src/services/oidc.ts \
  src/services/tokens.ts \
  migrations/001_init.sql \
  migrations/002_seed_minima.sql \
  scripts/migrate.ts \
  scripts/seed.ts \
  scripts/gen-jwk.ts \
  .env.example \
  docker-compose.dev.yml \
  Dockerfile \
  package.json \
  tsconfig.json \
  README.md

echo ""
echo "✅ ¡Estructura creada con éxito en la carpeta '$ROOT_DIR'!"
echo ""
echo "Contenido generado:"
# Muestra la estructura de árbol creada
ls -R
